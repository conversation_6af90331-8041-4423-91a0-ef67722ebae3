<?xml version="1.0" encoding="UTF-8"?>
<svg width="2250" height="812" viewBox="0 0 2250 812" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <!-- 定义渐变和滤镜 -->
  <defs>
    <!-- 主要渐变 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2D3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1A202C;stop-opacity:1" />
    </linearGradient>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>

    <!-- 悬浮效果滤镜 -->
    <filter id="hoverGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#DD6B20" flood-opacity="0.3"/>
    </filter>

    <!-- 图标符号定义 -->
    <symbol id="heartIcon" viewBox="0 0 24 24">
      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#DD6B20"/>
    </symbol>

    <symbol id="stepIcon" viewBox="0 0 24 24">
      <path d="M14,12l-2,2l-2,-2l2,-2l2,2zm-2,6l-4,-4l4,-4l4,4l-4,4z" fill="#DD6B20"/>
    </symbol>

    <symbol id="calorieIcon" viewBox="0 0 24 24">
      <path d="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2M21,9V7L15,1L9,7V9H21M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10Z" fill="#DD6B20"/>
    </symbol>

    <!-- 动画定义 -->
    <style>
      .pulse {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
      }

      .dashAnimation {
        stroke-dasharray: 5,5;
        animation: dash 2s linear infinite;
      }

      @keyframes dash {
        to { stroke-dashoffset: -10; }
      }
    </style>
  </defs>

  <!-- 页面1：启动页/登录页 -->
  <g id="page1" transform="translate(0,0)">
    <!-- 手机边框 -->
    <rect x="0" y="0" width="375" height="812" fill="none" stroke="#E2E8F0" stroke-width="1" rx="25"/>

    <!-- 背景渐变 -->
    <rect x="1" y="1" width="373" height="810" fill="url(#primaryGradient)" rx="24"/>

    <!-- 背景图片占位 -->
    <image x="50" y="100" width="275" height="200" href="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop" opacity="0.3" rx="15"/>

    <!-- 应用Logo区域 -->
    <circle cx="187.5" cy="200" r="40" fill="#F7FAFC" filter="url(#cardShadow)"/>
    <use href="#heartIcon" x="167.5" y="180" width="40" height="40" class="pulse"/>

    <!-- 应用标题 -->
    <text x="187.5" y="270" text-anchor="middle" fill="#F7FAFC" font-family="Arial, sans-serif" font-size="28" font-weight="bold">HealthPro</text>
    <text x="187.5" y="295" text-anchor="middle" fill="#A0AEC0" font-family="Arial, sans-serif" font-size="14">Your Personal Health Assistant</text>

    <!-- 登录表单区域 -->
    <rect x="30" y="400" width="315" height="280" fill="#F7FAFC" rx="20" filter="url(#cardShadow)"/>

    <!-- 输入框 -->
    <rect x="50" y="430" width="275" height="45" fill="#EDF2F7" rx="8"/>
    <text x="60" y="455" fill="#4A5568" font-family="Arial, sans-serif" font-size="14">Email Address</text>

    <rect x="50" y="490" width="275" height="45" fill="#EDF2F7" rx="8"/>
    <text x="60" y="515" fill="#4A5568" font-family="Arial, sans-serif" font-size="14">Password</text>

    <!-- 登录按钮 -->
    <rect x="50" y="560" width="275" height="50" fill="#DD6B20" rx="25" filter="url(#hoverGlow)"/>
    <text x="187.5" y="590" text-anchor="middle" fill="#F7FAFC" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Sign In</text>

    <!-- 底部链接 -->
    <text x="187.5" y="640" text-anchor="middle" fill="#718096" font-family="Arial, sans-serif" font-size="12">Don't have an account? Sign Up</text>
  </g>

  <!-- 页面2：数据仪表盘 -->
  <g id="page2" transform="translate(375,0)">
    <!-- 手机边框 -->
    <rect x="0" y="0" width="375" height="812" fill="none" stroke="#E2E8F0" stroke-width="1" rx="25"/>

    <!-- 背景 -->
    <rect x="1" y="1" width="373" height="810" fill="#F7FAFC" rx="24"/>

    <!-- 顶部状态栏 -->
    <rect x="1" y="1" width="373" height="60" fill="#1A202C" rx="24"/>
    <rect x="1" y="35" width="373" height="26" fill="#1A202C"/>

    <!-- 用户信息卡片 -->
    <rect x="20" y="80" width="335" height="100" fill="#FFFFFF" rx="15" filter="url(#cardShadow)"/>
    <circle cx="60" cy="130" r="25" fill="#E2E8F0"/>
    <image x="35" y="105" width="50" height="50" href="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" rx="25"/>

    <text x="100" y="125" fill="#1A202C" font-family="Arial, sans-serif" font-size="18" font-weight="bold">John Smith</text>
    <text x="100" y="145" fill="#718096" font-family="Arial, sans-serif" font-size="14">Today's Progress</text>

    <!-- 通知图标 -->
    <circle cx="320" cy="130" r="15" fill="#DD6B20"/>
    <text x="320" y="135" text-anchor="middle" fill="#F7FAFC" font-family="Arial, sans-serif" font-size="12" font-weight="bold">3</text>

    <!-- 核心健康指标卡片组 -->
    <!-- 心率卡片 -->
    <rect x="20" y="200" width="105" height="120" fill="#FFFFFF" rx="12" filter="url(#cardShadow)"/>
    <use href="#heartIcon" x="35" y="215" width="24" height="24"/>
    <text x="35" y="250" fill="#718096" font-family="Arial, sans-serif" font-size="12">Heart Rate</text>
    <text x="35" y="275" fill="#1A202C" font-family="Arial, sans-serif" font-size="24" font-weight="bold">72</text>
    <text x="35" y="295" fill="#718096" font-family="Arial, sans-serif" font-size="12">bpm</text>
    <circle cx="90" cy="280" r="8" fill="#22C55E" class="pulse"/>

    <!-- 步数卡片 -->
    <rect x="135" y="200" width="105" height="120" fill="#FFFFFF" rx="12" filter="url(#cardShadow)"/>
    <use href="#stepIcon" x="150" y="215" width="24" height="24"/>
    <text x="150" y="250" fill="#718096" font-family="Arial, sans-serif" font-size="12">Steps</text>
    <text x="150" y="275" fill="#1A202C" font-family="Arial, sans-serif" font-size="24" font-weight="bold">8,547</text>
    <text x="150" y="295" fill="#718096" font-family="Arial, sans-serif" font-size="12">of 10,000</text>

    <!-- 卡路里卡片 -->
    <rect x="250" y="200" width="105" height="120" fill="#FFFFFF" rx="12" filter="url(#cardShadow)"/>
    <use href="#calorieIcon" x="265" y="215" width="24" height="24"/>
    <text x="265" y="250" fill="#718096" font-family="Arial, sans-serif" font-size="12">Calories</text>
    <text x="265" y="275" fill="#1A202C" font-family="Arial, sans-serif" font-size="24" font-weight="bold">1,847</text>
    <text x="265" y="295" fill="#718096" font-family="Arial, sans-serif" font-size="12">kcal</text>

    <!-- 实时数据折线图区域 -->
    <rect x="20" y="340" width="335" height="200" fill="#FFFFFF" rx="15" filter="url(#cardShadow)"/>
    <text x="35" y="365" fill="#1A202C" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Heart Rate Trend</text>
    <text x="35" y="385" fill="#718096" font-family="Arial, sans-serif" font-size="12">Last 24 hours</text>

    <!-- 折线图坐标轴 -->
    <line x1="50" y1="500" x2="340" y2="500" stroke="#E2E8F0" stroke-width="1"/>
    <line x1="50" y1="410" x2="50" y2="500" stroke="#E2E8F0" stroke-width="1"/>

    <!-- 折线图数据线 -->
    <polyline points="50,480 80,460 110,470 140,450 170,440 200,455 230,445 260,435 290,450 320,440"
              fill="none" stroke="#DD6B20" stroke-width="3" class="dashAnimation"/>

    <!-- 数据点 -->
    <circle cx="50" cy="480" r="3" fill="#DD6B20"/>
    <circle cx="140" cy="450" r="3" fill="#DD6B20"/>
    <circle cx="230" cy="445" r="3" fill="#DD6B20"/>
    <circle cx="320" cy="440" r="3" fill="#DD6B20"/>

    <!-- 快速操作按钮区域 -->
    <rect x="20" y="560" width="160" height="50" fill="#DD6B20" rx="25"/>
    <text x="100" y="590" text-anchor="middle" fill="#F7FAFC" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Start Workout</text>

    <rect x="195" y="560" width="160" height="50" fill="#FFFFFF" rx="25" stroke="#DD6B20" stroke-width="2"/>
    <text x="275" y="590" text-anchor="middle" fill="#DD6B20" font-family="Arial, sans-serif" font-size="14" font-weight="bold">View Details</text>

    <!-- 底部导航栏 -->
    <rect x="1" y="750" width="373" height="61" fill="#1A202C"/>
    <rect x="1" y="750" width="373" height="25" fill="#1A202C" rx="0"/>

    <!-- 导航图标 -->
    <circle cx="75" cy="775" r="20" fill="#DD6B20"/>
    <circle cx="150" cy="775" r="15" fill="#4A5568"/>
    <circle cx="225" cy="775" r="15" fill="#4A5568"/>
    <circle cx="300" cy="775" r="15" fill="#4A5568"/>
  </g>

</svg>